package com.akey.core.dao.service.impl;

import com.akey.common.constant.SystemConstant;
import com.akey.common.exception.BusinessException;
import com.akey.core.cache.UserCacheService;
import com.akey.core.dao.service.UserTypeService;
import com.akey.core.dao.service.UserTypeMenuService;
import com.akey.common.enums.BuiltinEnum;
import com.akey.core.dao.entity.User;
import com.akey.core.dao.entity.UserType;
import com.akey.core.dao.mapper.UserMapper;
import com.akey.core.dao.mapper.UserTypeMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * 用户类型Service实现类
 * 
 * <p>使用MyBatis-Plus的QueryWrapper进行单表操作</p>
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserTypeServiceImpl implements UserTypeService {

    private final UserTypeMapper userTypeMapper;
    private final UserMapper userMapper;
    private final UserCacheService userCacheService;
    private final UserTypeMenuService userTypeMenuService;

    /**
     * 创建用户类型
     * 
     * @param userType 用户类型信息
     * @return 创建成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createUserType(UserType userType) {
        log.info("开始创建用户类型, typeName: {}", userType.getTypeName());
        
        // 校验参数
        if (!StringUtils.hasText(userType.getTypeName())) {
            log.warn("用户类型名称不能为空");
            throw new BusinessException("用户类型名称不能为空");
        }

        // 检查是否尝试使用超级管理员类型ID
        if (StringUtils.hasText(userType.getId()) && SystemConstant.ADMIN_USER_TYPE_ID.equals(userType.getId())) {
            log.warn("不允许使用超级管理员类型ID: {}", userType.getId());
            throw new BusinessException("该类型ID为系统保留，不允许使用");
        }

        // 检查类型名称是否已存在
        if (existsByTypeName(userType.getTypeName(), null)) {
            log.warn("用户类型名称已存在: {}", userType.getTypeName());
            throw new BusinessException("用户类型名称已存在");
        }
        
        // 设置默认值
        if (userType.getIsBuiltin() == null) {
            userType.setIsBuiltin(BuiltinEnum.NOT_BUILTIN);
        }
        
        try {
            int result = userTypeMapper.insert(userType);
            log.info("创建用户类型完成, typeName: {}, result: {}", userType.getTypeName(), result);
            return result > 0;
        } catch (Exception e) {
            log.error("创建用户类型失败, typeName: {}", userType.getTypeName(), e);
            throw e;
        }
    }

    /**
     * 修改用户类型
     * 
     * @param userType 用户类型信息（必须包含id）
     * @return 修改成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserType(UserType userType) {
        log.info("开始修改用户类型, id: {}, typeName: {}", userType.getId(), userType.getTypeName());
        
        // 校验参数
        if (!StringUtils.hasText(userType.getId())) {
            log.warn("用户类型ID不能为空");
            throw new BusinessException("用户类型ID不能为空");
        }
        
        if (!StringUtils.hasText(userType.getTypeName())) {
            log.warn("用户类型名称不能为空");
            throw new BusinessException("用户类型名称不能为空");
        }

        // 超级管理员类型不能修改
        if (SystemConstant.ADMIN_USER_TYPE_ID.equals(userType.getId())) {
            log.warn("超级管理员类型不能修改, id: {}", userType.getId());
            throw new BusinessException("该类型不可被操作");
        }

        // 检查用户类型是否存在
        UserType existingUserType = getUserTypeById(userType.getId());
        if (existingUserType == null) {
            log.warn("用户类型不存在, id: {}", userType.getId());
            throw new BusinessException("该类型不存在");
        }
        
        // 检查类型名称是否已被其他记录使用
        if (existsByTypeName(userType.getTypeName(), userType.getId())) {
            log.warn("用户类型名称已存在: {}", userType.getTypeName());
            throw new BusinessException("类型名称已存在");
        }
        
        try {
            int result = userTypeMapper.updateById(userType);
            log.info("修改用户类型完成, id: {}, typeName: {}, result: {}",
                    userType.getId(), userType.getTypeName(), result);

            // 修改成功后清除用户类型相关缓存
            if (result > 0) {
                userCacheService.clearUserTypeCache(userType.getId());
                // 清除所有用户的权限和角色缓存，因为用户类型变更可能影响权限
                userCacheService.clearAllUserPermissionsAndRolesCache();
                log.debug("已清除用户类型缓存和所有用户权限角色缓存, userTypeId: {}", userType.getId());
            }

            return result > 0;
        } catch (Exception e) {
            log.error("修改用户类型失败, id: {}, typeName: {}",
                    userType.getId(), userType.getTypeName(), e);
            throw e;
        }
    }

    /**
     * 删除用户类型
     * 
     * @param id 用户类型ID
     * @return 删除成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUserType(String id) {
        log.info("开始删除用户类型, id: {}", id);
        
        // 校验参数
        if (!StringUtils.hasText(id)) {
            log.warn("用户类型ID不能为空");
            throw new BusinessException("用户类型ID不能为空");
        }

        // 超级管理员类型不能删除
        if (SystemConstant.ADMIN_USER_TYPE_ID.equals(id)) {
            log.warn("超级管理员类型不能删除, id: {}", id);
            throw new BusinessException("该类型不可被操作");
        }
        
        // 检查用户类型是否存在
        UserType userType = getUserTypeById(id);
        if (userType == null) {
            log.warn("用户类型不存在, id: {}", id);
            throw new BusinessException("该类型不存在");
        }
        
        
        // 检查是否有用户正在使用该类型
        if (hasUsersWithType(id)) {
            log.warn("该用户类型正在被使用，无法删除, id: {}, typeName: {}", id, userType.getTypeName());
            throw new BusinessException("该类型正在被使用，无法删除");
        }
        
        try {
            // 先清空用户类型与菜单的关联关系
            int removedMenuCount = userTypeMenuService.removeAllMenusFromUserType(id);
            log.info("清空用户类型菜单关联关系, userTypeId: {}, 清空数量: {}", id, removedMenuCount);

            // 删除用户类型
            int result = userTypeMapper.deleteById(id);
            log.info("删除用户类型完成, id: {}, typeName: {}, result: {}",
                    id, userType.getTypeName(), result);

            // 删除成功后清除用户类型相关缓存
            if (result > 0) {
                userCacheService.clearUserTypeCache(id);
                // 清除所有用户的权限和角色缓存，因为用户类型删除可能影响权限
                userCacheService.clearAllUserPermissionsAndRolesCache();
                log.debug("已清除用户类型缓存和所有用户权限角色缓存, userTypeId: {}", id);
            }

            return result > 0;
        } catch (Exception e) {
            log.error("删除用户类型失败, id: {}", id, e);
            throw e;
        }
    }

    /**
     * 分页查询用户类型列表
     * 
     * @param page 分页参数
     * @param typeName 类型名称（模糊查询，可为空）
     * @param isBuiltin 是否内置（可为空，查询全部）
     * @return 分页结果
     */
    @Override
    public IPage<UserType> getUserTypePageList(Page<UserType> page, String typeName, Integer isBuiltin) {
        log.debug("开始分页查询用户类型列表, page: {}, size: {}, typeName: {}, isBuiltin: {}", 
                page.getCurrent(), page.getSize(), typeName, isBuiltin);
        
        LambdaQueryWrapper<UserType> queryWrapper = new LambdaQueryWrapper<>();
        
        // 类型名称模糊查询
        queryWrapper.like(StringUtils.hasText(typeName), UserType::getTypeName, typeName);
        
        // 是否内置筛选
        queryWrapper.eq(isBuiltin != null, UserType::getIsBuiltin, isBuiltin);
        
        // 排序：内置类型优先，然后按创建时间倒序
        queryWrapper.orderByAsc(UserType::getIsBuiltin)
                   .orderByDesc(UserType::getCreateTime);
        
        IPage<UserType> result = userTypeMapper.selectPage(page, queryWrapper);
        
        log.debug("分页查询用户类型列表完成, total: {}, pages: {}", 
                result.getTotal(), result.getPages());
        
        return result;
    }

    /**
     * 根据用户ID获取用户类型列表，需要排除超级管理员以及自身的类型
     * 
     * @param userId 当前用户ID
     * @return 可分配的用户类型列表
     */
    @Override
    public List<UserType> getAvailableUserTypesForUser(String userId) {
        log.debug("开始获取用户可分配的类型列表, userId: {}", userId);
        
        if (!StringUtils.hasText(userId)) {
            log.warn("用户ID不能为空");
            return List.of();
        }
        
        // 获取当前用户的类型ID
        User currentUser = userMapper.selectById(userId);
        String currentUserTypeId = (currentUser != null) ? currentUser.getUserTypeId() : null;
        
        LambdaQueryWrapper<UserType> queryWrapper = new LambdaQueryWrapper<>();
        
        // 排除超级管理员类型
        queryWrapper.ne(UserType::getId, SystemConstant.ADMIN_USER_TYPE_ID);
        
        // 排除用户自身的类型
        queryWrapper.ne(StringUtils.hasText(currentUserTypeId), UserType::getId, currentUserTypeId);
        
        // 按是否内置和创建时间排序
        queryWrapper.orderByAsc(UserType::getIsBuiltin)
                   .orderByAsc(UserType::getCreateTime);
        
        List<UserType> result = userTypeMapper.selectList(queryWrapper);
        
        log.debug("获取用户可分配的类型列表完成, userId: {}, count: {}", userId, result.size());
        
        return result;
    }

    /**
     * 根据类型名称查询用户类型
     * 
     * @param typeName 类型名称
     * @return 用户类型，如果不存在则返回null
     */
    @Override
    public UserType getUserTypeByName(String typeName) {
        if (!StringUtils.hasText(typeName)) {
            return null;
        }
        
        LambdaQueryWrapper<UserType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserType::getTypeName, typeName);
        
        return userTypeMapper.selectOne(queryWrapper);
    }

    /**
     * 根据ID查询用户类型
     * 
     * @param id 用户类型ID
     * @return 用户类型，如果不存在则返回null
     */
    @Override
    public UserType getUserTypeById(String id) {
        if (!StringUtils.hasText(id)) {
            return null;
        }
        
        return userTypeMapper.selectById(id);
    }

    /**
     * 检查类型名称是否已存在
     * 
     * @param typeName 类型名称
     * @param excludeId 要排除的ID（用于修改时排除自己），可为空
     * @return 存在返回true，不存在返回false
     */
    @Override
    public boolean existsByTypeName(String typeName, String excludeId) {
        if (!StringUtils.hasText(typeName)) {
            return false;
        }
        
        LambdaQueryWrapper<UserType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserType::getTypeName, typeName);
        
        // 排除指定ID
        queryWrapper.ne(StringUtils.hasText(excludeId), UserType::getId, excludeId);
        
        Long count = userTypeMapper.selectCount(queryWrapper);
        return count != null && count > 0;
    }

    /**
     * 检查是否有用户正在使用指定的用户类型
     * 
     * @param userTypeId 用户类型ID
     * @return 有用户使用返回true，没有返回false
     */
    @Override
    public boolean hasUsersWithType(String userTypeId) {
        if (!StringUtils.hasText(userTypeId)) {
            return false;
        }
        
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUserTypeId, userTypeId);
        
        Long count = userMapper.selectCount(queryWrapper);
        return count != null && count > 0;
    }

    /**
     * 获取所有用户类型列表
     *
     * @return 用户类型列表
     */
    @Override
    public List<UserType> getAllUserTypes() {
        LambdaQueryWrapper<UserType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(UserType::getIsBuiltin)
                   .orderByDesc(UserType::getCreateTime);

        return userTypeMapper.selectList(queryWrapper);
    }

    /**
     * 获取所有用户类型列表,如果登录用户非超级管理员类型，则需要排除
     *
     * @return 用户类型列表
     */
    @Override
    public List<UserType> getAllUserTypes(String userId) {
        LambdaQueryWrapper<UserType> queryWrapper = new LambdaQueryWrapper<>();

        // 判断用户是否为超级管理员
        User userById = userMapper.selectById(userId);
        if (userById != null && !isSuperAdminType(userById.getUserTypeId())) {
            queryWrapper.ne(UserType::getId, SystemConstant.ADMIN_USER_TYPE_ID);
        }
        queryWrapper.orderByAsc(UserType::getIsBuiltin)
                .orderByDesc(UserType::getCreateTime);



        return userTypeMapper.selectList(queryWrapper);
    }

    /**
     * 统计指定用户类型的用户数量
     *
     * @param userTypeId 用户类型ID
     * @return 用户数量
     */
    @Override
    public long countUsersByUserType(String userTypeId) {
        if (!StringUtils.hasText(userTypeId)) {
            return 0;
        }

        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUserTypeId, userTypeId);

        Long count = userMapper.selectCount(queryWrapper);
        return count != null ? count : 0;
    }

    /**
     * 分页查询用户类型列表（排除超级管理员类型）
     *
     * @param page 分页参数
     * @param typeName 类型名称（模糊查询，可为空）
     * @param isBuiltin 是否内置（可为空，查询全部）
     * @return 分页结果
     */
    @Override
    public IPage<UserType> getUserTypePageListExcludeAdmin(Page<UserType> page, String typeName, Integer isBuiltin) {
        log.debug("开始分页查询用户类型列表（排除超级管理员）, page: {}, size: {}, typeName: {}, isBuiltin: {}",
                page.getCurrent(), page.getSize(), typeName, isBuiltin);

        LambdaQueryWrapper<UserType> queryWrapper = new LambdaQueryWrapper<>();

        // 排除超级管理员类型
        queryWrapper.ne(UserType::getId, SystemConstant.ADMIN_USER_TYPE_ID);

        // 类型名称模糊查询
        queryWrapper.like(StringUtils.hasText(typeName), UserType::getTypeName, typeName);

        // 是否内置筛选
        queryWrapper.eq(isBuiltin != null, UserType::getIsBuiltin, isBuiltin);

        // 排序：内置类型优先，然后按创建时间倒序
        queryWrapper.orderByAsc(UserType::getIsBuiltin)
                   .orderByDesc(UserType::getCreateTime);

        IPage<UserType> result = userTypeMapper.selectPage(page, queryWrapper);

        log.debug("分页查询用户类型列表（排除超级管理员）完成, total: {}, pages: {}",
                result.getTotal(), result.getPages());

        return result;
    }

    /**
     * 判断指定的用户类型是否为超级管理员类型
     *
     * <p>通过用户类型ID与系统常量中的超级管理员类型ID进行比较</p>
     *
     * @param userTypeId 用户类型ID
     * @return 是超级管理员类型返回true，否则返回false
     */
    @Override
    public boolean isSuperAdminType(String userTypeId) {
        if (!StringUtils.hasText(userTypeId)) {
            return false;
        }

        return Objects.equals(userTypeId, SystemConstant.ADMIN_USER_TYPE_ID);
    }

    /**
     * 判断指定的用户类型对象是否为超级管理员类型
     *
     * <p>通过用户类型对象的ID与系统常量中的超级管理员类型ID进行比较</p>
     *
     * @param userType 用户类型对象
     * @return 是超级管理员类型返回true，否则返回false
     */
    @Override
    public boolean isSuperAdminType(UserType userType) {
        if (userType == null) {
            return false;
        }

        return isSuperAdminType(userType.getId());
    }


}