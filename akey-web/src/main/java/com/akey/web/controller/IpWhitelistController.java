package com.akey.web.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import com.akey.common.Result;
import com.akey.common.annotation.OperationLog;
import com.akey.common.annotation.OperationModule;
import com.akey.common.constant.SystemConstant;
import com.akey.common.enums.EnableStatusEnum;
import com.akey.common.enums.OperationType;
import com.akey.core.dto.IpWhitelistDTO;
import com.akey.core.dto.IpWhitelistQueryDTO;
import com.akey.core.dao.entity.IpWhitelist;
import com.akey.core.dao.entity.User;
import com.akey.core.dao.service.IpWhitelistService;
import com.akey.core.dao.service.UserService;
import com.akey.core.dao.service.UserTypeService;
import com.akey.core.vo.IpWhitelistWithUserVO;
import com.akey.framework.web.annotation.GoogleAuthRequired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;

import java.util.List;

/**
 * IP白名单Controller
 *
 * <p>提供IP白名单管理的REST接口</p>
 * <p>包含分页查询、添加、更新、删除等功能</p>
 *
 * <p>权限控制：</p>
 * <ul>
 *   <li>查询：需要security:ip:query权限</li>
 *   <li>添加：需要security:ip:add权限</li>
 *   <li>更新：需要security:ip:update权限</li>
 *   <li>删除：需要security:ip:delete权限</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Slf4j
@RestController
@RequestMapping("/system/ip-whitelist")
@RequiredArgsConstructor
@Validated
@OperationModule("IP白名单管理")
public class IpWhitelistController {

    private final IpWhitelistService ipWhitelistService;
    private final UserService userService;
    private final UserTypeService userTypeService;

    /**
     * 分页查询IP白名单列表
     *
     * <p>支持根据用户名查询，通过连表查询获取用户信息</p>
     * <p>权限分级查询：</p>
     * <ul>
     *   <li>超级管理员：可以查看所有用户的白名单记录</li>
     *   <li>普通用户：只能查看自己的白名单记录</li>
     * </ul>
     * <p>按创建时间倒序排列</p>
     *
     * @param queryDTO 查询参数
     * @return 分页结果（包含用户信息）
     */
    @GetMapping("/page")
    @SaCheckPermission(value = "security:ip:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    public Result<IPage<IpWhitelistWithUserVO>> getIpWhitelistPage(@Valid IpWhitelistQueryDTO queryDTO) {
        try {
            // 获取当前登录用户ID
            String currentUserId = StpUtil.getLoginIdAsString();
            User user = userService.getUserById(currentUserId);
            log.debug("分页查询IP白名单列表, currentUserId: {}, queryDTO: {}", currentUserId, queryDTO);

            // 权限控制：普通用户只能查看自己的白名单

            if (userTypeService.isSuperAdminType(user.getUserTypeId())) {
                currentUserId = null;
            }
            // 创建分页对象
            Page<IpWhitelistWithUserVO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
            // 执行分页查询（带用户信息）
            IPage<IpWhitelistWithUserVO> pageResult = ipWhitelistService.selectListPage(
                    page, queryDTO.getWhitelistType(), queryDTO.getIpAddress(), currentUserId, queryDTO.getUsername(), queryDTO.getEnableStatus()
            );

            log.debug("分页查询IP白名单列表成功, currentUserId: {}, total: {}, pages: {}",
                    currentUserId, pageResult.getTotal(), pageResult.getPages());
            return Result.success("查询成功", pageResult);
        } catch (Exception e) {
            log.error("分页查询IP白名单列表失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }


    /**
     * 添加IP白名单
     *
     * <p>会检查IP地址格式的有效性</p>
     * <p>会检查是否存在重复的白名单记录</p>
     *
     * @param dto IP白名单信息
     * @return 添加结果
     */
    @GoogleAuthRequired(required = false)
    @OperationLog(value = "添加IP白名单", type = OperationType.CREATE)
    @PostMapping
    @SaCheckPermission(value = "security:ip:add", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    public Result<String> addIpWhitelist(@Valid @RequestBody IpWhitelistDTO dto) {
        try {
            log.info("添加IP白名单, dto: {}", dto);

            // 获取登录用户ID
            String currentUserId = StpUtil.getLoginIdAsString();
            // 转换为实体对象
            IpWhitelist ipWhitelist = convertToEntity(dto);
            ipWhitelist.setUserId(currentUserId);

            // 执行添加
            boolean success = ipWhitelistService.addIpWhitelist(ipWhitelist);
            if (success) {
                log.info("添加IP白名单成功, id: {}", ipWhitelist.getId());
                return Result.success("添加成功", ipWhitelist.getId());
            } else {
                return Result.error("添加失败");
            }

        } catch (Exception e) {
            log.error("添加IP白名单失败, dto: {}", dto, e);
            return Result.error("添加失败：" + e.getMessage());
        }
    }

    /**
     * 更新IP白名单
     *
     * <p>会检查IP地址格式的有效性</p>
     * <p>会检查是否存在重复的白名单记录（排除自己）</p>
     *
     * @param dto IP白名单信息（必须包含id）
     * @return 更新结果
     */
    @GoogleAuthRequired(required = false)
    @OperationLog(value = "更新IP白名单", type = OperationType.UPDATE)
    @PutMapping
    @SaCheckPermission(value = "security:ip:update", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    public Result<String> updateIpWhitelist(@Valid @RequestBody IpWhitelistDTO dto) {
        try {
            log.info("更新IP白名单, dto: {}", dto);

            if (!dto.isUpdate()) {
                return Result.error("更新操作必须提供ID");
            }

            // 转换为实体对象
            IpWhitelist ipWhitelist = convertToEntity(dto);

            // 执行更新
            boolean success = ipWhitelistService.updateIpWhitelist(ipWhitelist);
            if (success) {
                log.info("更新IP白名单成功, id: {}", dto.getId());
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }

        } catch (Exception e) {
            log.error("更新IP白名单失败, dto: {}", dto, e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除IP白名单
     *
     * <p>逻辑删除指定的IP白名单记录</p>
     *
     * @param id IP白名单ID
     * @return 删除结果
     */
    @GoogleAuthRequired(required = false)
    @OperationLog(value = "删除IP白名单", type = OperationType.DELETE)
    @DeleteMapping("/{id}")
    @SaCheckPermission(value = "security:ip:delete", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    public Result<String> deleteIpWhitelist(@PathVariable @NotBlank(message = "ID不能为空") String id) {
        try {
            log.info("删除IP白名单, id: {}", id);

            // 执行删除
            boolean success = ipWhitelistService.deleteIpWhitelist(id);
            if (success) {
                log.info("删除IP白名单成功, id: {}", id);
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }

        } catch (Exception e) {
            log.error("删除IP白名单失败, id: {}", id, e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除IP白名单
     *
     * <p>批量逻辑删除指定的IP白名单记录</p>
     *
     * @param ids IP白名单ID列表
     * @return 删除结果
     */
    @GoogleAuthRequired(required = false)
    @OperationLog(value = "批量删除IP白名单", type = OperationType.DELETE)
    @DeleteMapping("/batch")
    @SaCheckPermission(value = "security:ip:delete", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    public Result<String> batchDeleteIpWhitelist(@RequestBody @NotEmpty(message = "ID列表不能为空") List<String> ids) {
        try {
            log.info("批量删除IP白名单, ids: {}", ids);

            int successCount = ipWhitelistService.batchDeleteIpWhitelist(ids);

            log.info("批量删除IP白名单完成, 成功数量: {}, 总数量: {}", successCount, ids.size());

            if (successCount > 0) {
                return Result.success(String.format("删除成功，共删除%d条记录", successCount));
            } else {
                return Result.error("删除失败，没有记录被删除");
            }

        } catch (Exception e) {
            log.error("批量删除IP白名单失败, ids: {}", ids, e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新IP白名单状态
     *
     * <p>批量修改指定白名单的启用状态</p>
     *
     * @param ids          白名单ID列表
     * @param enableStatus 要设置的状态
     * @return 更新结果
     */
    @GoogleAuthRequired(required = false)
    @OperationLog(value = "批量更新IP白名单状态", type = OperationType.UPDATE)
    @PutMapping("/batch/status")
    @SaCheckPermission(value = "security:ip:update", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    public Result<String> batchUpdateStatus(@RequestBody @NotEmpty(message = "ID列表不能为空") List<String> ids,
                                            @RequestParam EnableStatusEnum enableStatus) {
        try {
            log.info("批量更新IP白名单状态, ids: {}, enableStatus: {}", ids, enableStatus);

            int successCount = ipWhitelistService.batchUpdateStatus(ids, enableStatus);

            log.info("批量更新IP白名单状态完成, 成功数量: {}, 总数量: {}", successCount, ids.size());

            if (successCount > 0) {
                String statusText = enableStatus == EnableStatusEnum.ENABLED ? "启用" : "禁用";
                return Result.success(String.format("%s成功，共%s%d条记录", statusText, statusText, successCount));
            } else {
                return Result.error("更新失败，没有记录被更新");
            }

        } catch (Exception e) {
            log.error("批量更新IP白名单状态失败, ids: {}, enableStatus: {}", ids, enableStatus, e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }


    /**
     * 重置用户白名单
     *
     * @param userId 用户ID
     * @return 重置结果
     */
    @OperationLog(value = "重置用户白名单", type = OperationType.RESET)
    @GoogleAuthRequired(required = false)
    @DeleteMapping("/reset")
    @SaCheckPermission(value = "security:ip:delete", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    public Result<String> resetUserWhitelist(@RequestParam(required = false) String userId) {
        try {
            return ipWhitelistService.deleteIpWhitelistByUserId(userId) ? Result.success("重置成功") : Result.error("重置失败");
        } catch (Exception e) {
            log.error("重置用户白名单失败, userId: {}", userId, e);
            return Result.error("重置失败：" + e.getMessage());
        }
    }


    /**
     * 将DTO对象转换为实体对象
     *
     * @param dto DTO对象
     * @return 实体对象
     */
    private IpWhitelist convertToEntity(IpWhitelistDTO dto) {
        if (dto == null) {
            return null;
        }

        IpWhitelist entity = new IpWhitelist();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

}
