/*
 Navicat Premium Dump SQL

 Source Server         : local
 Source Server Type    : MySQL
 Source Server Version : 80042 (8.0.42)
 Source Host           : localhost:3306
 Source Schema         : akey_admin

 Target Server Type    : MySQL
 Target Server Version : 80042 (8.0.42)
 File Encoding         : 65001

 Date: 04/08/2025 22:19:34
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for login_log
-- ----------------------------
DROP TABLE IF EXISTS `login_log`;
CREATE TABLE `login_log` (
  `id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `create_by` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  `update_by` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记：0-未删除，1-已删除',
  `version` int NOT NULL DEFAULT '1' COMMENT '乐观锁版本号',
  `user_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户ID，登录失败时可能为空',
  `username` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '登录用户名，即使登录失败也记录',
  `login_time` datetime NOT NULL COMMENT '登录时间',
  `login_status` tinyint(1) NOT NULL COMMENT '登录状态：0-失败，1-成功',
  `failure_reason` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '登录失败原因',
  `session_id` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '会话ID，登录成功时记录',
  `client_ip` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端IP地址（支持IPv6）',
  `is_proxy` tinyint(1) DEFAULT '0' COMMENT '是否通过代理：0-否，1-是',
  `proxy_chain` text COLLATE utf8mb4_unicode_ci COMMENT '代理链信息，记录X-Forwarded-For等',
  `location` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地理位置(格式：国家 省份 城市)',
  `user_agent` text COLLATE utf8mb4_unicode_ci COMMENT '完整的User-Agent字符串',
  `device_type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备类型：DESKTOP, MOBILE, TABLET, BOT',
  `device_brand` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备品牌：Apple, Samsung, Huawei等',
  `device_model` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备型号',
  `os_name` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作系统名称：Windows, macOS, Android, iOS等',
  `os_version` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作系统版本',
  `browser_name` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '浏览器名称：Chrome, Firefox, Safari等',
  `browser_version` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '浏览器版本',
  `device_fingerprint` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备指纹（MD5哈希）',
  `risk_level` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT 'LOW' COMMENT '风险等级：LOW-低风险，MEDIUM-中风险，HIGH-高风险',
  `is_suspicious` tinyint(1) DEFAULT '0' COMMENT '是否可疑登录：0-否，1-是',
  `suspicious_reasons` json DEFAULT NULL COMMENT '可疑原因列表，JSON格式',
  `request_uri` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '请求URI',
  `request_method` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '请求方法：GET, POST等',
  `referer` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '来源页面',
  `language` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户端语言',
  `timezone` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户端时区',
  `extra_data` json DEFAULT NULL COMMENT '扩展数据，JSON格式，用于存储额外信息',
  `tags` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标签，逗号分隔，用于分类和检索',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_username` (`username`),
  KEY `idx_login_time` (`login_time`),
  KEY `idx_login_status` (`login_status`),
  KEY `idx_client_ip` (`client_ip`),
  KEY `idx_device_fingerprint` (`device_fingerprint`),
  KEY `idx_risk_level` (`risk_level`),
  KEY `idx_is_suspicious` (`is_suspicious`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_user_login_time` (`user_id`,`login_time`),
  KEY `idx_username_login_time` (`username`,`login_time`),
  KEY `idx_ip_login_time` (`client_ip`,`login_time`),
  KEY `idx_status_time` (`login_status`,`login_time`),
  KEY `idx_suspicious_time` (`is_suspicious`,`login_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录日志表';

-- ----------------------------
-- Records of login_log
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for operation_log
-- ----------------------------
DROP TABLE IF EXISTS `operation_log`;
CREATE TABLE `operation_log` (
  `id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键ID',
  `user_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作用户ID',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作用户名',
  `operation_time` datetime NOT NULL COMMENT '操作时间',
  `operation_module` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作模块(用户自定义，如：用户管理、角色管理、系统管理等)',
  `operation_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型(CREATE-新增,UPDATE-修改,DELETE-删除,QUERY-查询,LOGIN-登录,LOGOUT-登出)',
  `operation_desc` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作描述',
  `request_method` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '请求方法(GET,POST,PUT,DELETE等)',
  `request_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '请求URL',
  `request_params` text COLLATE utf8mb4_unicode_ci COMMENT '请求参数(JSON格式)',
  `response_result` longtext COLLATE utf8mb4_unicode_ci COMMENT '响应结果(JSON格式，支持大数据量)',
  `response_status` int DEFAULT NULL COMMENT '响应状态码',
  `response_message` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '响应消息',
  `client_ip` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户端IP地址(支持IPv6)',
  `user_agent` varchar(1000) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户代理信息',
  `execution_time` bigint DEFAULT NULL COMMENT '操作耗时(毫秒)',
  `location` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地理位置(格式：国家 省份 城市)',
  `operation_status` tinyint NOT NULL DEFAULT '1' COMMENT '操作状态(0-失败,1-成功)',
  `error_message` varchar(1000) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '错误信息(操作失败时记录)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标记(0-未删除,1-已删除)',
  `version` int NOT NULL DEFAULT '1' COMMENT '乐观锁版本号',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_operation_module` (`operation_module`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operation_status` (`operation_status`),
  KEY `idx_client_ip` (`client_ip`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_user_time` (`user_id`,`operation_time`),
  KEY `idx_module_type` (`operation_module`,`operation_type`),
  KEY `idx_status_time` (`operation_status`,`operation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- ----------------------------
-- Records of operation_log
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for sys_ip_whitelist
-- ----------------------------
DROP TABLE IF EXISTS `sys_ip_whitelist`;
CREATE TABLE `sys_ip_whitelist` (
  `id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键ID，使用UUID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记：0-未删除，1-已删除',
  `version` int NOT NULL DEFAULT '1' COMMENT '乐观锁版本号',
  `whitelist_type` tinyint(1) NOT NULL COMMENT '白名单类型：1-系统白名单，2-接口白名单',
  `ip_address` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'IP地址或IP段（CIDR格式），如：************* 或 ***********/24',
  `user_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联用户ID，用于区分是哪个用户的白名单',
  `enable_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '启用状态：0-禁用，1-启用',
  `remark` text COLLATE utf8mb4_unicode_ci COMMENT '备注说明，记录IP用途等信息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_type_ip_user` (`whitelist_type`,`ip_address`,`user_id`,`deleted`) COMMENT '同类型下IP地址和用户组合唯一（考虑逻辑删除）',
  KEY `idx_whitelist_type` (`whitelist_type`) COMMENT '白名单类型索引，用于按类型查询',
  KEY `idx_ip_address` (`ip_address`) COMMENT 'IP地址索引，用于IP匹配查询',
  KEY `idx_user_id` (`user_id`) COMMENT '用户ID索引，用于查询用户的白名单',
  KEY `idx_enable_status` (`enable_status`) COMMENT '启用状态索引，用于查询有效白名单',
  KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引，用于时间排序',
  KEY `idx_type_status` (`whitelist_type`,`enable_status`) COMMENT '类型+状态复合索引，用于快速查找有效白名单',
  KEY `idx_type_ip_status` (`whitelist_type`,`ip_address`,`enable_status`) COMMENT '类型+IP+状态复合索引，用于IP验证查询',
  KEY `idx_user_type` (`user_id`,`whitelist_type`) COMMENT '用户+类型复合索引，用于查询用户特定类型的白名单',
  KEY `idx_user_status` (`user_id`,`enable_status`) COMMENT '用户+状态复合索引，用于查询用户有效的白名单'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='IP白名单表';

-- ----------------------------
-- Records of sys_ip_whitelist
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单ID',
  `parent_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '父菜单ID(顶级菜单为null)',
  `menu_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单名称',
  `menu_type` int NOT NULL DEFAULT '2' COMMENT '菜单类型(1:目录 2:菜单 3:按钮)',
  `menu_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单编码(唯一标识符)',
  `route_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '路由路径',
  `component_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组件路径',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图标',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序号',
  `visible` int NOT NULL DEFAULT '1' COMMENT '是否可见(0:隐藏 1:显示)',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态(0:禁用 1:启用)',
  `permission` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '权限标识',
  `external_link` int NOT NULL DEFAULT '0' COMMENT '是否外部链接(0:否 1:是)',
  `open_mode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '_self' COMMENT '打开方式(_self:当前窗口 _blank:新窗口)',
  `route_params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '路由参数(JSON格式)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'system' COMMENT '创建人ID',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `deleted` int NOT NULL DEFAULT '0' COMMENT '逻辑删除(0:未删除 1:已删除)',
  `is_builtin` int NOT NULL DEFAULT '1' COMMENT '是否内置类型 (0:是内置,不可删除、修改状态 1:否,可删除)',
  `version` int NOT NULL DEFAULT '1' COMMENT '版本号',
  `link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '链接地址(用于外部链接)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_menu_code` (`menu_code`,(if((`deleted` = 0),0,NULL))) USING BTREE,
  KEY `idx_parent_id` (`parent_id`) USING BTREE,
  KEY `idx_menu_type` (`menu_type`) USING BTREE,
  KEY `idx_visible_status` (`visible`,`status`) USING BTREE,
  KEY `idx_sort_order` (`sort_order`) USING BTREE,
  KEY `idx_deleted` (`deleted`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='菜单表';

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
BEGIN;
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('05e13bf414bdf5586751e44a3a8fa4e8', 'e07a147c8c79e847b9268aa112e8f1bc', '创建菜单', 3, 'MENU_1753865966609_XRMR', '', '', '', 0, 1, 1, 'menu:create', 0, '_self', '', '2025-07-30 16:59:29', '7245b87945b676f847b0235fc8856f6d', NULL, NULL, 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('09db664e37a207a2aee8f569b7660aa5', 'cfbc7793e1bb878bdf4b3df3c6f276e3', '分配权限', 3, 'MENU_1752993429536_6UFB', '', '', '', 0, 1, 1, 'user:type:assignMenu', 0, '_self', '', '2025-07-20 14:37:15', '7245b87945b676f847b0235fc8856f6d', '2025-07-30 16:58:45', '7245b87945b676f847b0235fc8856f6d', 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('0a9f73e83e46c7c2a2cd96cda0e2355d', 'e0ba2d74b831246c4c025ff6d85ed40d', '修改白名单', 3, 'MENU_1754314378177_R4LF', '', '', '', 2, 1, 1, 'security:ip:update', 0, '_self', '', '2025-08-04 21:33:00', '7245b87945b676f847b0235fc8856f6d', NULL, NULL, 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('12928f6365e56e0feb6eb5a638886c43', '3ec4e4d0bae55059825aeaa433d641d6', '注销用户', 3, 'MENU_1754313934248_JCDB', '', '', '', 1, 1, 1, 'online:user:manage', 0, '_self', '', '2025-08-04 21:25:40', '7245b87945b676f847b0235fc8856f6d', NULL, NULL, 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('25a7d40f8b8988ddd855a9d99f44f174', 'e9c2b477ca39138eeac5da5c41c335d9', '解冻用户', 3, 'MENU_1753865741214_2HEO', '', '', '', 0, 1, 1, 'user:unlock', 0, '_self', '', '2025-07-30 16:55:51', '7245b87945b676f847b0235fc8856f6d', NULL, NULL, 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('3507a7bb5fdf0f4faf04551db514cff9', 'e9c2b477ca39138eeac5da5c41c335d9', '新增用户', 3, 'MENU_1753864730179_2HXR', '', '', '', 0, 1, 1, 'user:add', 0, '_self', '', '2025-07-30 16:39:20', '7245b87945b676f847b0235fc8856f6d', NULL, NULL, 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('35b4b9b074b879296861cf4abb1d75d9', 'e9c2b477ca39138eeac5da5c41c335d9', '删除用户', 3, 'MENU_1753864771013_7DT3', '', '', '', 0, 1, 1, 'user:del', 0, '_self', '', '2025-07-30 16:39:37', '7245b87945b676f847b0235fc8856f6d', '2025-07-30 16:54:36', '7245b87945b676f847b0235fc8856f6d', 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('3ec4e4d0bae55059825aeaa433d641d6', '98ea0f96dc882c55fc2338a762ce961d', '在线用户', 2, 'MENU_1754155813874_79QY', '/online', 'OnlineUserManagement', 'user', 3, 1, 1, 'online:user:view', 0, '_self', '', '2025-08-03 01:30:24', '7245b87945b676f847b0235fc8856f6d', '2025-08-04 21:15:26', '7245b87945b676f847b0235fc8856f6d', 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('432d2fdaf13a5d559c87846169ecf652', 'e9c2b477ca39138eeac5da5c41c335d9', '重置密码', 3, 'MENU_1753865694911_DJ58', '', '', '', 2, 1, 1, 'user:reset:password', 0, '_self', '', '2025-07-30 16:55:09', '7245b87945b676f847b0235fc8856f6d', '2025-08-04 21:23:54', '7245b87945b676f847b0235fc8856f6d', 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('533b965d252992b01f77efd70203bbea', 'e9c2b477ca39138eeac5da5c41c335d9', '重置谷歌验证', 3, 'MENU_1754313695785_MHP8', '', '', '', 1, 1, 1, 'user:reset:google', 0, '_self', '', '2025-08-04 21:21:43', '7245b87945b676f847b0235fc8856f6d', '2025-08-04 21:23:14', '7245b87945b676f847b0235fc8856f6d', 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('53f3fb520af88c1310f1f9046493e1c0', '53f8c9944c5a027c40a01b4ea8882bd3', '登录统计', 2, 'MENU_1753888128043_CMSQ', '/login-chart', 'LoginLogAnalytics', '', 2, 1, 1, 'login:log:view', 0, '_self', '', '2025-07-30 23:09:55', '7245b87945b676f847b0235fc8856f6d', '2025-08-01 15:24:10', '7245b87945b676f847b0235fc8856f6d', 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('53f8c9944c5a027c40a01b4ea8882bd3', NULL, '日志管理', 1, 'MENU_1753880378280_Q268', '/log', '', 'application-two', 4, 1, 1, '', 0, '_self', '', '2025-07-30 20:59:48', '7245b87945b676f847b0235fc8856f6d', '2025-08-04 21:15:02', '7245b87945b676f847b0235fc8856f6d', 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('55fc15c0ff7321c60147dcc58335d247', 'e07a147c8c79e847b9268aa112e8f1bc', '修改菜单', 3, 'MENU_1753865981086_DVR5', '', '', '', 0, 1, 1, 'menu:update', 0, '_self', '', '2025-07-30 16:59:43', '7245b87945b676f847b0235fc8856f6d', NULL, NULL, 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('6070385d15386032a274aa1fb7025fac', 'cfbc7793e1bb878bdf4b3df3c6f276e3', '修改类型', 3, 'MENU_1752993354160_P5OU', '', '', '', 0, 1, 1, 'user:type:edit', 0, '_self', '', '2025-07-20 14:36:19', '7245b87945b676f847b0235fc8856f6d', NULL, NULL, 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('6f533d5f7adb50f40f5c3ad199039943', 'e9c2b477ca39138eeac5da5c41c335d9', '修改用户', 3, 'MENU_1753864784142_BKDR', '', '', '', 0, 1, 1, 'user:edit', 0, '_self', '', '2025-07-30 16:39:50', '7245b87945b676f847b0235fc8856f6d', '2025-07-30 16:54:41', '7245b87945b676f847b0235fc8856f6d', 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('8400c73fa64edf7cc6106e17c79f9f9c', 'e4asc4429ax21c149afbf4c2353fb924', '系统资源', 2, 'MENU_1754076403921_GMV0', '/resource', 'SystemResourceMonitor', 'application-one', 4, 1, 1, 'sys:resource:view', 0, '_self', '', '2025-08-02 03:26:45', '7245b87945b676f847b0235fc8856f6d', '2025-08-02 03:28:44', '7245b87945b676f847b0235fc8856f6d', 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('9068507fa9f51f159b726daf250002d1', '3ec4e4d0bae55059825aeaa433d641d6', '踢出用户', 3, 'MENU_1754313950905_F1MD', '', '', '', 2, 1, 1, 'online:user:manage', 0, '_self', '', '2025-08-04 21:25:57', '7245b87945b676f847b0235fc8856f6d', NULL, NULL, 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('914962847e9e206ee1243eace73f801b', NULL, '安全配置', 1, 'MENU_1753985014563_X19J', '/security', '', 'api', 3, 1, 1, '', 0, '_self', '', '2025-08-01 02:04:30', '7245b87945b676f847b0235fc8856f6d', NULL, NULL, 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('94e026b906a91438111b46d74f36b8f6', '53f8c9944c5a027c40a01b4ea8882bd3', '操作日志', 2, 'MENU_1753902698193_MEZZ', '/operation', 'OperationLogManagement', '', 3, 1, 1, 'operation:log:view', 0, '_self', '', '2025-07-31 03:11:52', '7245b87945b676f847b0235fc8856f6d', '2025-08-01 15:24:18', NULL, 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('98ea0f96dc882c55fc2338a762ce961d', NULL, '用户管理', 1, 'MENU_1754313278244_N5W5', '/user', '', 'user', 2, 1, 1, '', 0, '_self', '', '2025-08-04 21:14:55', '7245b87945b676f847b0235fc8856f6d', NULL, NULL, 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('9cd764db13b89cf0d8af9dc75cfed494', '53f8c9944c5a027c40a01b4ea8882bd3', '登录日志', 2, 'MENU_1753882971461_0B3I', '/login', 'LoginLogManagement', '', 1, 1, 1, 'login:log:view', 0, '_self', '', '2025-07-30 21:43:25', '7245b87945b676f847b0235fc8856f6d', '2025-08-01 23:52:05', '7245b87945b676f847b0235fc8856f6d', 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('acd3c450889a7088df45bc904390eccd', 'e0ba2d74b831246c4c025ff6d85ed40d', '添加白名单', 3, 'MENU_1754314361287_BEMD', '', '', '', 1, 1, 1, 'security:ip:add', 0, '_self', '', '2025-08-04 21:32:44', '7245b87945b676f847b0235fc8856f6d', NULL, NULL, 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('ada00361449a0bb5c4ecbb070a2275a4', '9cd764db13b89cf0d8af9dc75cfed494', '删除日志', 3, 'MENU_1754314469068_E1UM', '', '', '', 1, 1, 1, 'login:log:delete', 0, '_self', '', '2025-08-04 21:34:31', '7245b87945b676f847b0235fc8856f6d', NULL, NULL, 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('c96588e516853cf6150208132a019856', 'e9c2b477ca39138eeac5da5c41c335d9', '冻结用户', 3, 'MENU_1753865731343_YGM1', '', '', '', 0, 1, 1, 'user:lock', 0, '_self', '', '2025-07-30 16:55:33', '7245b87945b676f847b0235fc8856f6d', NULL, NULL, 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('cfbc7793e1bb878bdf4b3df3c6f276e3', '98ea0f96dc882c55fc2338a762ce961d', '用户类型', 2, 'MENU_1752689033927_FM7G', '/user-type', 'UserTypeManagement', 'address-book', 2, 1, 1, 'user:type:view', 0, '_self', '', '2025-07-17 02:05:00', '7245b87945b676f847b0235fc8856f6d', '2025-08-04 21:24:18', '7245b87945b676f847b0235fc8856f6d', 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('de69f61d0687a72ee3fbf7c4aec5cb40', 'cfbc7793e1bb878bdf4b3df3c6f276e3', '增加类型', 3, 'MENU_1752993388415_RINQ', '', '', '', 0, 1, 1, 'user:type:add', 0, '_self', '', '2025-07-20 14:36:33', '7245b87945b676f847b0235fc8856f6d', NULL, NULL, 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('e063d8b7dd4aff95e3bd873dee027c41', 'cfbc7793e1bb878bdf4b3df3c6f276e3', '删除类型', 3, 'MENU_1752993409535_BT45', '', '', '', 0, 1, 1, 'user:type:delete', 0, '_self', '', '2025-07-20 14:36:53', '7245b87945b676f847b0235fc8856f6d', NULL, NULL, 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('e07a147c8c79e847b9268aa112e8f1bc', 'e4asc4429ax21c149afbf4c2353fb924', '菜单管理', 2, 'menu_management', '/menu', 'MenuManagement', 'application-two', 3, 1, 1, 'menu:view', 0, '_self', NULL, '2025-07-08 10:32:55', 'system', '2025-08-01 23:52:26', '7245b87945b676f847b0235fc8856f6d', 0, 0, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('e0ba2d74b831246c4c025ff6d85ed40d', '914962847e9e206ee1243eace73f801b', 'IP白名单', 2, 'MENU_1753985086072_QO83', '/ip', 'IpWhitelist', '', 1, 1, 1, 'security:ip:view', 0, '_self', '', '2025-08-01 02:05:04', '7245b87945b676f847b0235fc8856f6d', '2025-08-04 21:32:16', '7245b87945b676f847b0235fc8856f6d', 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('e4asc4429ax21c149afbf4c2353fb924', NULL, '系统管理', 1, 'system', '/system', NULL, 'application-one', 1, 1, 1, '', 0, '_self', NULL, '2025-07-09 18:49:15', 'system', '2025-07-30 17:57:52', '7245b87945b676f847b0235fc8856f6d', 0, 0, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('e6e25f91060bc491fd2af542b0d20ffa', 'e07a147c8c79e847b9268aa112e8f1bc', '删除菜单', 3, 'MENU_1753865993138_VCP7', '', '', '', 0, 1, 1, 'menu:delete', 0, '_self', '', '2025-07-30 16:59:56', '7245b87945b676f847b0235fc8856f6d', NULL, NULL, 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('e9c2b477ca39138eeac5da5c41c335d9', '98ea0f96dc882c55fc2338a762ce961d', '用户管理', 2, 'MENU_1753269027502_5W2F', '/user', 'UserManagement', 'address-book', 0, 1, 1, 'user:view', 0, '_self', '', '2025-07-23 19:10:59', '7245b87945b676f847b0235fc8856f6d', '2025-08-04 21:15:14', '7245b87945b676f847b0235fc8856f6d', 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('ecfead2efd081f2333916cfa648955ab', 'e0ba2d74b831246c4c025ff6d85ed40d', '删除白名单', 3, 'MENU_1754314392254_9PBN', '', '', '', 3, 1, 1, 'security:ip:delete', 0, '_self', '', '2025-08-04 21:33:16', '7245b87945b676f847b0235fc8856f6d', NULL, NULL, 0, 1, 1, NULL);
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_code`, `route_path`, `component_path`, `icon`, `sort_order`, `visible`, `status`, `permission`, `external_link`, `open_mode`, `route_params`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `is_builtin`, `version`, `link`) VALUES ('f97f2d76bc55a176d55211b7c8d1165c', '94e026b906a91438111b46d74f36b8f6', '删除日志', 3, 'MENU_1754314528887_XL39', '', '', '', 1, 1, 1, 'operation:log:delete', 0, '_self', '', '2025-08-04 21:35:30', '7245b87945b676f847b0235fc8856f6d', NULL, NULL, 0, 1, 1, NULL);
COMMIT;

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user` (
  `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户密码',
  `user_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户类型ID',
  `google_auth_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '谷歌验证KEY',
  `account_locked` int NOT NULL DEFAULT '0' COMMENT '账户是否锁定 (0:未锁定 1:锁定)',
  `password_update_time` datetime DEFAULT NULL COMMENT '密码最后修改时间',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后登录IP',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'system' COMMENT '创建人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',
  `deleted` int NOT NULL DEFAULT '0' COMMENT '逻辑删除标志 (0:未删除 1:已删除)',
  `version` int NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_username` (`username`,(if((`deleted` = 0),0,NULL))) USING BTREE,
  KEY `idx_user_type_id` (`user_type_id`) USING BTREE,
  KEY `idx_account_locked` (`account_locked`) USING BTREE,
  KEY `idx_deleted` (`deleted`) USING BTREE,
  CONSTRAINT `fk_user_type_id` FOREIGN KEY (`user_type_id`) REFERENCES `sys_user_type` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='用户表';

-- ----------------------------
-- Records of sys_user
-- ----------------------------
BEGIN;
INSERT INTO `sys_user` (`id`, `username`, `password`, `user_type_id`, `google_auth_key`, `account_locked`, `password_update_time`, `last_login_time`, `last_login_ip`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `version`) VALUES ('7245b87945b676f847b0235fc8856f6d', 'admin', '$2a$12$1yaNMbOv3TaebCgreM7fxeDqf.qHGwACRH4G4KpeMsr9I231G3.bu', 'd875b80b81bf273489639a6075c662f6', NULL, 0, '2025-07-31 20:34:41', '2025-08-04 22:14:56', '127.0.0.1', '2025-07-04 10:50:44', 'system', '2025-08-04 14:14:56', NULL, 0, 0);
COMMIT;

-- ----------------------------
-- Table structure for sys_user_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_type`;
CREATE TABLE `sys_user_type` (
  `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键ID',
  `type_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '类型名称',
  `is_builtin` int NOT NULL DEFAULT '1' COMMENT '是否内置类型 (0:是内置,不可删除 1:否,可删除)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'system' COMMENT '创建人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',
  `deleted` int NOT NULL DEFAULT '0' COMMENT '逻辑删除标志 (0:未删除 1:已删除)',
  `version` int NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_type_name` (`type_name`,(if((`deleted` = 0),0,NULL))) USING BTREE,
  KEY `idx_is_builtin` (`is_builtin`) USING BTREE,
  KEY `idx_deleted` (`deleted`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='用户类型表';

-- ----------------------------
-- Records of sys_user_type
-- ----------------------------
BEGIN;
INSERT INTO `sys_user_type` (`id`, `type_name`, `is_builtin`, `create_time`, `create_by`, `update_time`, `update_by`, `deleted`, `version`) VALUES ('d875b80b81bf273489639a6075c662f6', '超级管理员', 0, '2025-07-04 10:49:40', 'system', NULL, NULL, 0, 0);
COMMIT;

-- ----------------------------
-- Table structure for sys_user_type_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_type_menu`;
CREATE TABLE `sys_user_type_menu` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关联ID',
  `user_type_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户类型ID',
  `menu_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'system' COMMENT '创建人ID',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `deleted` int NOT NULL DEFAULT '0' COMMENT '逻辑删除(0:未删除 1:已删除)',
  `version` int NOT NULL DEFAULT '1' COMMENT '版本号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_user_type_menu` (`user_type_id`,`menu_id`,(if((`deleted` = 0),0,NULL))) USING BTREE,
  KEY `idx_user_type_id` (`user_type_id`) USING BTREE,
  KEY `idx_menu_id` (`menu_id`) USING BTREE,
  KEY `idx_deleted` (`deleted`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='用户类型菜单关联表';

-- ----------------------------
-- Records of sys_user_type_menu
-- ----------------------------
BEGIN;
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
