# 系统接口权限清单

## 权限说明
- 所有权限都支持超级管理员角色（`SystemConstant.ADMIN_USER_TYPE_ID`）访问
- 使用Sa-Token框架进行权限控制，通过`@SaCheckPermission`注解实现
- 权限格式：`模块:操作`，如`user:view`、`menu:create`等

## 1. 认证管理模块（AuthController）

| 接口路径 | HTTP方法 | 功能描述 | 所需权限 | 备注 |
|---------|---------|---------|---------|------|
| `/auth/login` | POST | 用户登录 | 无 | 公开接口 |
| `/auth/captcha` | GET | 获取验证码 | 无 | 公开接口 |
| `/auth/isLogin` | GET | 检查登录状态 | 无 | 公开接口 |
| `/auth/logout` | POST | 用户退出登录 | 需要登录 | 需要登录状态 |

## 2. 菜单管理模块（MenuController）

| 接口路径 | HTTP方法 | 功能描述 | 所需权限 | 备注 |
|---------|---------|---------|---------|------|
| `/menu/getMenus` | GET | 获取所有菜单 | `menu:view` | 支持筛选条件 |
| `/menu` | POST | 创建菜单 | `menu:create` | 新增菜单功能 |
| `/menu` | PUT | 更新菜单 | `menu:update` | 修改菜单功能 |
| `/menu/{id}` | DELETE | 删除菜单 | `menu:delete` | 检查子菜单和关联关系 |
| `/menu/directories` | GET | 获取所有目录菜单 | `menu:view` | 用于父菜单选项 |

## 3. 用户管理模块（UserController）

| 接口路径 | HTTP方法 | 功能描述 | 所需权限 | 备注 |
|---------|---------|---------|---------|------|
| `/user/page` | GET | 分页查询用户列表 | `user:view` | 支持多种筛选条件 |
| `/user/types` | GET | 获取全部用户类型列表 | `user:view` | 用于下拉框选择 |
| `/user` | POST | 新增用户 | `user:add` | 创建新用户 |
| `/user/{id}` | PUT | 修改用户信息 | `user:edit` | 修改用户基本信息 |
| `/user/{id}` | DELETE | 删除用户 | `user:del` | 逻辑删除，需要谷歌验证 |
| `/user/{id}/reset-password` | PUT | 重置用户密码 | `user:resetPassword` | 重置密码功能 |
| `/user/change-password` | PUT | 修改当前用户密码 | 需要登录 | 用户自己修改密码 |
| `/user/{id}/reset-google-auth` | PUT | 重置用户谷歌验证KEY | `user:resetGoogleAuth` | 重置谷歌验证器 |

## 4. 用户类型管理模块（UserTypeController）

| 接口路径 | HTTP方法 | 功能描述 | 所需权限 | 备注 |
|---------|---------|---------|---------|------|
| `/user-type/page` | GET | 分页查询用户类型列表 | `user:type:view` | 支持筛选条件 |
| `/user-type/list` | GET | 获取所有用户类型列表 | `user:type:view` | 用于下拉选择 |
| `/user-type` | POST | 新增用户类型 | `user:type:add` | 创建新用户类型 |
| `/user-type/{id}` | PUT | 修改用户类型 | `user:type:edit` | 修改用户类型信息 |
| `/user-type/{id}` | DELETE | 删除用户类型 | `user:type:delete` | 删除用户类型 |
| `/user-type/{id}/assignMenus` | POST | 为用户类型分配菜单权限 | `user:type:assignMenu` | 权限分配功能 |
| `/user-type/{id}/menus` | GET | 获取用户类型已分配的菜单 | `user:type:view` | 查看已分配权限 |

## 5. 在线用户管理模块（OnlineUserController）

| 接口路径 | HTTP方法 | 功能描述 | 所需权限 | 备注 |
|---------|---------|---------|---------|------|
| `/online-user/list` | GET | 获取在线用户列表 | `online-user:view` | 分页查询在线用户 |
| `/online-user/kickout` | POST | 踢出用户 | `online-user:manage` | 踢出指定用户 |
| `/online-user/batch-kickout` | POST | 批量踢出用户 | `online-user:manage` | 批量踢出功能 |
| `/online-user/statistics` | GET | 获取在线用户统计信息 | `online-user:view` | 统计数据 |

## 6. IP白名单管理模块（IpWhitelistController）

| 接口路径 | HTTP方法 | 功能描述 | 所需权限 | 备注 |
|---------|---------|---------|---------|------|
| `/system/ip-whitelist/page` | GET | 分页查询IP白名单列表 | `ip:whitelist:query` | 支持用户名查询 |
| `/system/ip-whitelist` | POST | 添加IP白名单 | `ip:whitelist:add` | 新增白名单记录 |
| `/system/ip-whitelist` | PUT | 更新IP白名单 | `ip:whitelist:update` | 修改白名单记录 |
| `/system/ip-whitelist/{id}` | DELETE | 删除IP白名单 | `ip:whitelist:delete` | 删除白名单记录 |

## 7. 操作日志管理模块（OperationLogController）

| 接口路径 | HTTP方法 | 功能描述 | 所需权限 | 备注 |
|---------|---------|---------|---------|------|
| `/operation-log/page` | GET | 分页查询操作日志 | `operation:log:view` | 支持多种筛选条件 |
| `/operation-log/failures` | GET | 获取失败操作记录 | `operation:log:view` | 查看失败记录 |
| `/operation-log/ip/{clientIp}` | GET | 根据IP地址查询操作记录 | `operation:log:view` | IP相关操作记录 |
| `/operation-log/cleanup` | DELETE | 清理过期的操作日志 | `operation:log:delete` | 清理指定天数前的记录 |
| `/operation-log/clear` | DELETE | 清空所有操作日志 | `operation:log:delete` | 物理删除所有记录 |

## 8. 登录日志管理模块（LoginLogController）

| 接口路径 | HTTP方法 | 功能描述 | 所需权限 | 备注 |
|---------|---------|---------|---------|------|
| `/login-log/page` | GET | 分页查询登录日志 | `login:log:view` | 支持多种筛选条件 |
| `/login-log/clear` | DELETE | 清空所有登录日志 | `login:log:delete` | 物理删除所有记录 |

## 9. 系统资源管理模块（SystemResourceController）

| 接口路径 | HTTP方法 | 功能描述 | 所需权限 | 备注 |
|---------|---------|---------|---------|------|
| `/system/resource` | GET | 获取系统资源信息 | `system:resource:view` | 系统监控信息 |

## 10. 谷歌验证器管理模块（GoogleAuthController）

| 接口路径 | HTTP方法 | 功能描述 | 所需权限 | 备注 |
|---------|---------|---------|---------|------|
| `/google-auth/setup` | GET | 生成谷歌验证器设置信息 | 需要登录 | 获取设置二维码 |
| `/google-auth/setup` | POST | 设置谷歌验证器 | 需要登录 | 绑定谷歌验证器 |
| `/google-auth/verify` | POST | 验证谷歌验证码 | 需要登录 | 验证TOTP码 |
| `/google-auth/reset` | DELETE | 重置谷歌验证器 | 需要登录 | 解绑谷歌验证器 |
| `/google-auth/status` | GET | 获取谷歌验证器状态 | 需要登录 | 查看绑定状态 |

## 权限汇总

### 查看权限（View）
- `menu:view` - 菜单查看
- `user:view` - 用户查看
- `user:type:view` - 用户类型查看
- `online-user:view` - 在线用户查看
- `ip:whitelist:query` - IP白名单查询
- `operation:log:view` - 操作日志查看
- `login:log:view` - 登录日志查看
- `system:resource:view` - 系统资源查看

### 新增权限（Add/Create）
- `menu:create` - 菜单创建
- `user:add` - 用户新增
- `user:type:add` - 用户类型新增
- `ip:whitelist:add` - IP白名单新增

### 修改权限（Edit/Update）
- `menu:update` - 菜单修改
- `user:edit` - 用户修改
- `user:type:edit` - 用户类型修改
- `user:type:assignMenu` - 用户类型权限分配
- `online-user:manage` - 在线用户管理
- `ip:whitelist:update` - IP白名单修改
- `user:resetPassword` - 重置用户密码
- `user:resetGoogleAuth` - 重置谷歌验证器

### 删除权限（Delete）
- `menu:delete` - 菜单删除
- `user:del` - 用户删除
- `user:type:delete` - 用户类型删除
- `ip:whitelist:delete` - IP白名单删除
- `operation:log:delete` - 操作日志删除
- `login:log:delete` - 登录日志删除

## 特殊说明

1. **超级管理员权限**：所有接口都支持超级管理员角色（`SystemConstant.ADMIN_USER_TYPE_ID`）访问
2. **谷歌验证器要求**：部分敏感操作（如删除用户）需要谷歌验证器验证
3. **IP白名单校验**：所有需要登录的接口都会进行IP白名单校验
4. **权限缓存**：系统使用Redis缓存用户权限信息，提高性能
5. **权限继承**：用户权限通过用户类型关联的菜单权限获得

---
*生成时间：2025-08-04*
*权限框架：Sa-Token*
*缓存方案：Redis*
